import type { PageContainerProps } from '@ant-design/pro-layout';
import { PageContainer } from '@ant-design/pro-layout';
import type { PropsWithChildren } from 'react';
import styled from 'styled-components';
import breakpoints from '../../utilities/breakpoints';

export type PageWithHeaderProps = Omit<PropsWithChildren<PageContainerProps>, 'breadcrumb' | 'breadcrumbRender'> & {
    isDashboard?: boolean;
};

const StyledPageHeaderWrapper = styled(PageContainer)<{ isDashboard?: boolean }>`
    .ant-page-header-compact .ant-page-header-heading {
        flex-wrap: nowrap;
    }

    .ant-page-header-heading-sub-title {
        margin-top: 3px;
    }

    @media screen and (max-width: ${breakpoints.md}) {
        .ant-page-header-heading-title {
            font-size: 16px;
        }
    }

    ${({ isDashboard }) =>
        isDashboard &&
        `
            margin-top: -48px !important;
            margin-bottom: -48px !important;

            & .ant-pro-page-container-warp {
                display: none;
            }

            & .ant-pro-grid-content {
                & .ant-pro-grid-content-children {
                    & .ant-pro-page-container-children-content {
                        margin-left: 0px;
                        margin-right: 0px;
                    }
                }
            }
        `}
`;

const ConsolePageWithHeader = (props: PageWithHeaderProps) => <StyledPageHeaderWrapper {...props} breadcrumb={null} />;

export default ConsolePageWithHeader;
