import { LeftOutlined } from '@ant-design/icons';
import type { PageContainerProps } from '@ant-design/pro-layout';
import { PageContainer } from '@ant-design/pro-layout';
import { headingXXLargeStyle } from '@porsche-design-system/components-react/styles';
import type { PropsWithChildren } from 'react';
import { useTranslation } from 'react-i18next';
import styled, { css } from 'styled-components';
import { LayoutType } from '../../api/types';
import breakpoints from '../../utilities/breakpoints';

const StyledPageHeaderWrapper = styled(PageContainer)<{ hasHeader?: boolean }>`
    ${props =>
        props?.theme?.layoutType === LayoutType.PorscheV3 &&
        css`
            & .ant-pro-page-container-warp {
                background-color: ${props =>
                    props?.theme?.layoutType === LayoutType.PorscheV3 ? 'transparent' : '#eeeff2'};
            }

            & .ant-pro-page-container-children-content {
                margin: 0;
            }
        `}

    & .ant-page-header {
        ${props =>
            props?.theme?.layoutType === LayoutType.PorscheV3
                ? css`
                      padding: 0 0 36px 0;
                      background-color: transparent;
                  `
                : css`
                      padding: 16px 24px;
                      background-color: #eeeff2;
                  `};

        ${props => !props?.hasHeader && 'padding: 0 !important;'}

        & .ant-page-header-heading-title {
            font-weight: 900;

            & > .ant-typography {
                font-size: 20px;
                font-weight: 900;
            }

            @media screen and (max-width: ${breakpoints.md}) {
                & > .ant-typography {
                    font-size: 16px;
                }
            }
        }
    }

    & .ant-pro-footer-bar {
        width: 100% !important;
    }

    & .ant-form-item-label > label {
        font-size: 16px;
    }

    & .ant-page-header-heading {
        position: relative;
        display: flex;
        align-items: center;
        min-height: 1.5rem;

        & .ant-page-header-heading-left {
            display: flex;
            position: relative;
            width: 100%;
            min-height: 1.5rem;
            overflow: inherit;
            ${props => props?.theme?.layoutType !== LayoutType.PorscheV3 && 'justify-content: center;'}

            & > .ant-page-header-back {
                position: absolute;
                left: 0;
            }

            ${props =>
                props?.theme?.layoutType === LayoutType.PorscheV3 &&
                css`
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    justify-content: center;
                    gap: 16px;

                    & > .ant-page-header-back {
                        position: relative;
                    }

                    & > .ant-page-header-heading-title {
                        ${headingXXLargeStyle}
                    }
                `}

            & > .ant-page-header-heading-title > h5 {
                margin-bottom: 0;
            }
        }

        & .ant-page-header-heading-extra {
            position: absolute;
            right: 0;
        }
    }

    @media screen and (min-width: ${breakpoints.md}) {
        & .ant-page-header {
            padding: ${props => (props?.theme?.layoutType === LayoutType.PorscheV3 ? '0 0 36px 0' : '16px 60px')};
        }
    }

    @media screen and (max-width: ${breakpoints.md}) {
        ${props =>
            props?.theme?.layoutType === LayoutType.PorscheV3 &&
            css`
                max-width: calc(100vw - clamp(16px, 1.25vw + 12px, 24px) - clamp(16px, 1.25vw + 12px, 24px));
            `}
    }

    @media screen and (max-width: ${breakpoints.sm}) {
        & .ant-page-header {
            & .ant-page-header-heading-extra {
                position: ${props => (props?.theme?.layoutType === LayoutType.PorscheV3 ? 'absolute' : 'inherit')};
            }
        }
    }
`;

export type PageWithHeaderProps = Omit<PropsWithChildren<PageContainerProps>, 'breadcrumb' | 'breadcrumbRender'>;

const BasicProPageWithHeader = (props: PageWithHeaderProps) => {
    const { t } = useTranslation('common');
    const { title, backIcon, header, onBack } = props;

    return (
        <StyledPageHeaderWrapper
            backIcon={
                <>
                    <LeftOutlined /> {t('common:actions.back')}
                </>
            }
            {...props}
            breadcrumb={null}
            hasHeader={!!title || !!backIcon || !!header || !!onBack}
        />
    );
};

export default BasicProPageWithHeader;
