import type { PageContainerProps } from '@ant-design/pro-layout';
import { PageContainer } from '@ant-design/pro-layout';
import type { PropsWithChildren } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import breakpoints from '../../utilities/breakpoints';
import BackIcon from '../../assets/ci/header/back.svg';

export type PageWithHeaderProps = Omit<
    PropsWithChildren<PageContainerProps>,
    'breadcrumb' | 'breadcrumbRender' | 'backIcon'
>;

const StyledPageHeaderWrapper = styled(PageContainer)`
    & .ant-page-header {
        background: #eeeff2;
        padding: 16px 24px;
    }

    & .ant-pro-page-container-children-content {
        margin: 24px 24px 0px;
    }

    & > .ant-pro-page-container-warp {
        position: fixed;
        top: 80px;
        width: 100%;
        z-index: 1;
    }

    & > .ant-pro-grid-content {
        margin-top: 104px;
    }

    @media screen and (min-width: ${breakpoints.md}) {
        & .ant-page-header {
            padding: 16px 60px;
        }
        & .ant-pro-page-container-children-content {
            margin: 24px 60px 0px;
        }
    }

    .ant-page-header-heading-left {
        position: relative;
        width: 100%;
        justify-content: center;
    }

    .ant-page-header-back {
        position: absolute;
        left: 0;
    }

    @media screen and (max-width: ${breakpoints.md}) {
        .back-text {
            display: none;
        }
        .ant-page-header-heading-title {
            font-size: 16px;
        }
    }
`;

const PageWithHeader = (props: PageWithHeaderProps) => {
    const { t } = useTranslation('ciConsoleLayout');

    return (
        <StyledPageHeaderWrapper
            {...props}
            backIcon={
                <span>
                    <BackIcon /> <span className="back-text">{t('ciConsoleLayout:header.back')}</span>
                </span>
            }
            breadcrumb={null}
        />
    );
};

export default PageWithHeader;
