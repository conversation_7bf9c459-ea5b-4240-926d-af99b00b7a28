import type { PageContainerProps } from '@ant-design/pro-layout';
import { PageContainer } from '@ant-design/pro-layout';
import { PIcon } from '@porsche-design-system/components-react';
import type { PropsWithChildren } from 'react';
import { useTranslation } from 'react-i18next';
import styled, { css } from 'styled-components';
import { LayoutType } from '../../api/types';
import breakpoints from '../../utilities/breakpoints';

const StyledButton = styled.div`
    margin-left: -7px;
    display: flex;
    font-family: var(--font-family);
    font-size: var(--button-font-size, 1rem);
    text-align: center;
`;

export type PageWithHeaderProps = Omit<
    PropsWithChildren<PageContainerProps>,
    'breadcrumb' | 'breadcrumbRender' | 'backIcon'
>;

const StyledPageHeaderWrapper = styled(PageContainer)`
    & .ant-page-header {
        ${props => props?.theme?.layoutType !== LayoutType.PorscheV3 && 'background: #eeeff2;'}
        padding: 16px 24px;
    }

    & .ant-pro-page-container-children-content {
        margin: 24px 24px 0px;
    }

    & > .ant-pro-page-container-warp {
        position: fixed;
        top: 80px;
        width: 100%;
        z-index: 100;
    }

    & > .ant-pro-grid-content {
        margin-top: 104px;
    }

    @media screen and (min-width: ${breakpoints.md}) {
        & .ant-page-header {
            padding: 16px 60px;
        }
        & .ant-pro-page-container-children-content {
            margin: 24px 60px 0px;
        }
    }

    .ant-page-header-heading-left {
        position: relative;
        width: 100%;
        ${props => props?.theme?.layoutType !== LayoutType.PorscheV3 && 'justify-content: center;'}
    }

    ${props =>
        props?.theme?.layoutType !== LayoutType.PorscheV3 &&
        css`
            .ant-page-header-back {
                position: absolute;
                left: 0;
            }
        `}

    @media screen and (max-width: ${breakpoints.md}) {
        .back-text {
            display: none;
        }
        .ant-page-header-heading-title {
            font-size: 16px;
        }
    }
`;

const PageWithHeader = (props: PageWithHeaderProps) => {
    const { t } = useTranslation('ciConsoleLayout');

    return (
        <StyledPageHeaderWrapper
            {...props}
            backIcon={
                <StyledButton>
                    <PIcon name="arrow-head-left" />
                    <div style={{ marginTop: '5px' }}>{t('ciConsoleLayout:header.back')}</div>
                </StyledButton>
            }
            breadcrumb={null}
        />
    );
};

export default PageWithHeader;
