import type { ThemeConfig } from 'antd';

// This replaces the Less variables from antd.override.less and global.less
export const antdThemeConfig: ThemeConfig = {
    token: {
        // based on your antd.override.less file:
        colorPrimary: 'var(--ant-primary-color)',

        // layout tokens (from variables.less)
        paddingContentHorizontalLG: 20, // @layout-body-padding horizontal
        paddingContentVertical: 10, // @layout-body-padding vertical

        // antd v5 by default have border radius of 6px, need to revert to default value from antd v4
        borderRadius: 2,

        // components use the full design system styles, not minimal wireframe styles
        wireframe: false,
    },
    components: {
        // from antd.override.less
        Segmented: {
            itemSelectedBg: 'var(--ant-primary-color)', // @segmented-selected-bg
            itemColor: 'rgba(0, 0, 0, 0.65)', // @segmented-label-color
            itemHoverColor: 'var(--ant-primary-color)', // @segmented-label-hover-color
        },

        Layout: {
            // from global.less body background
            bodyBg: 'var(--app-body-background-color, rgba(239, 239, 239, 0.93725))',
        },

        Tooltip: {
            // from global.less
            colorBgSpotlight: 'rgba(0, 0, 0, 0.8)', // Background from global.less
        },
    },
    // enable CSS Variables for dynamic theming compatibility
    cssVar: {
        prefix: 'ant',
        key: 'default',
    },
    // we only use one ConfigProvider, hence no need to hash
    // refer to https://ant.design/docs/react/css-variables#disable-hash
    hashed: false,
};

export default antdThemeConfig;
