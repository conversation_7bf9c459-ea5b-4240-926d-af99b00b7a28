{"name": "afc-v2", "version": "0.0.0-development", "private": true, "packageManager": "yarn@4.2.1", "license": "UNLICENSED", "engines": {"node": ">=20.2"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "scripts": {"build": "NODE_ENV=production ts-node --transpileOnly devtools/commands/build.ts", "build:istanbul": "USE_ISTANBUL=true yarn build", "dev": "ts-node --transpileOnly devtools/commands/dev.ts", "dev:fs": "CACHE_MODE=filesystem yarn dev", "dev:istanbul": "USE_ISTANBUL=true yarn dev", "dev:mock": "ts-node --transpileOnly src/mock/index.ts", "devil": "ts-node --transpileOnly devtools/commands/devil.ts", "generate:schema": "graphql-codegen", "generate:schema:integrations": "graphql-codegen --config codegen-integrations.ts", "generate:schema:porscheVehicleDataIntegrations": "graphql-codegen --config posrcheVehicleDataCodegen-integrations.ts", "generate:style": "ts-node --transpileOnly devtools/commands/extractTheme.ts", "generate:dataSets": "ts-node --transpileOnly devtools/commands/generateDataSets.ts", "generate:indexes": "ts-node --transpileOnly cypress/snapshots/generateIndexes.ts", "lint": "eslint . --ext .js,.ts,.tsx,.graphql", "lint:cache": "yarn lint --cache --cache-location ./.cache/.eslint/", "lint:fix": "yarn lint --fix", "lint:cache:fix": "yarn lint:cache --fix", "tsc": "tsc", "prepare": "husky install", "print-env": "ts-node --transpileOnly devtools/print-env.ts", "generate:api-index": "ts-node --transpileOnly devtools/codegen-generate-index.ts", "generate:api-cleanup": "ts-node --transpileOnly devtools/codegen-clean-up.ts", "prerelease": "node devtools/commands/prerelease.js", "github:start-background-run": "ts-node --transpileOnly ./devtools/github/background-run/start.ts", "test": "NODE_ENV=test jest --runInBand --verbose"}, "devDependencies": {"@actions/core": "^1.11.1", "@aws-sdk/client-s3": "^3.883.0", "@aws-sdk/client-ses": "^3.883.0", "@babel/core": "^7.28.4", "@babel/eslint-parser": "^7.28.4", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-numeric-separator": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/plugin-transform-runtime": "^7.28.3", "@babel/preset-env": "^7.28.3", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.1", "@cypress/code-coverage": "^3.14.6", "@graphql-codegen/add": "^5.0.3", "@graphql-codegen/cli": "^4.0.1", "@graphql-codegen/fragment-matcher": "^5.1.0", "@graphql-codegen/near-operation-file-preset": "^2.5.0", "@graphql-codegen/schema-ast": "^4.1.0", "@graphql-codegen/typescript": "^4.1.6", "@graphql-codegen/typescript-operations": "^4.6.1", "@graphql-codegen/typescript-react-apollo": "^3.3.7", "@graphql-codegen/typescript-resolvers": "4.0.6", "@jest/globals": "^29.7.0", "@octokit/rest": "^19.0.13", "@percy/cli": "^1.31.1", "@percy/cypress": "^3.1.6", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.17", "@promster/types": "^15.4.1", "@sentry/cli": "^2.53.0", "@svgr/webpack": "^8.1.0", "@swc/core": "^1.13.5", "@swc/helpers": "^0.5.17", "@types/accept-language-parser": "^1.5.8", "@types/adm-zip": "^0.5.7", "@types/archiver": "^6.0.3", "@types/bcryptjs": "^2.4.6", "@types/bull": "^4.10.4", "@types/chai": "^4.3.20", "@types/docusign-esign": "^5.19.9", "@types/express": "^4.17.23", "@types/file-saver": "^2.0.7", "@types/graphql-depth-limit": "^1.1.6", "@types/graphql-upload": "^8.0.12", "@types/http-proxy": "^1.17.16", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.10", "@types/loadable__component": "^5.13.10", "@types/lodash": "^4.17.20", "@types/marked": "^6.0.0", "@types/mjml": "^4.7.4", "@types/morgan": "^1.9.10", "@types/node": "^20.19.13", "@types/node-fetch": "^2.6.13", "@types/nodemailer": "^6.4.19", "@types/numeral": "^2.0.5", "@types/pubsub-js": "^1.8.6", "@types/qrcode": "^1.5.5", "@types/react": "^18.3.24", "@types/react-dom": "^18.3.7", "@types/react-helmet": "^6.1.11", "@types/react-responsive-masonry": "2.6.0", "@types/sanitize-html": "^2.16.0", "@types/ssh2-sftp-client": "^9.0.5", "@types/styled-components": "^5.1.34", "@types/swagger-ui-express": "^4.1.8", "@types/tail": "^2.2.3", "@types/url-join": "^4.0.3", "@types/wait-on": "^5.3.4", "@types/ws": "^8.18.1", "@types/zxcvbn": "^4.4.5", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "antd-dayjs-webpack-plugin": "^1.0.6", "archiver": "^7.0.1", "babel-jest": "^29.7.0", "babel-loader": "^10.0.0", "babel-plugin-import": "^1.13.8", "babel-plugin-istanbul": "^7.0.1", "babel-plugin-styled-components": "^2.1.4", "babel-plugin-transform-react-remove-prop-types": "^0.4.24", "copy-webpack-plugin": "^13.0.1", "css-loader": "^6.11.0", "css-minimizer-webpack-plugin": "^7.0.2", "cypress": "^13.17.0", "cypress-network-idle": "^1.15.0", "dotenv": "^16.6.1", "dotenv-expand": "^10.0.0", "dpdm": "^3.14.0", "esbuild": "^0.25.9", "eslint": "^8.57.1", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-graphql": "^4.0.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "execa": "^5.1.1", "file-loader": "^6.2.0", "glob": "^9.3.5", "graphql-tag": "^2.12.6", "http-proxy": "^1.18.1", "husky": "^9.1.7", "identity-obj-proxy": "^3.0.0", "immutability-helper": "^3.1.1", "jest": "^29.7.0", "less": "^4.4.1", "less-loader": "^12.3.0", "less-plugin-variables-output": "^1.2.0", "less-vars-to-js": "^1.3.0", "listen": "^1.0.1", "mime-types": "^3.0.1", "mini-css-extract-plugin": "^2.9.4", "null-loader": "^4.0.1", "octokit": "^2.1.0", "parse-duration": "^1.1.2", "postcss": "^8.5.6", "postcss-preset-env": "^10.3.1", "prettier": "^3.6.2", "react-dev-utils": "^12.0.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-refresh": "^0.14.2", "resize-observer-polyfill": "^1.5.1", "resolve-url-loader": "^5.0.0", "rimraf": "^6.0.1", "semantic-release": "^21.1.2", "showdown": "^2.1.0", "simple-git": "^3.28.0", "swc-loader": "^0.2.6", "swc-plugin-coverage-instrument": "^0.0.27", "tail": "^2.2.6", "test-listen": "^1.1.0", "ts-essentials": "^10.1.1", "ts-node": "^10.9.2", "typescript": "^5.9.2", "wait-on": "^7.2.0", "webpack": "^5.101.3", "webpack-bundle-analyzer": "^4.10.2", "webpack-dev-middleware": "^6.1.3", "webpack-hot-middleware": "^2.26.1", "webpack-manifest-plugin": "^5.0.1", "webpackbar": "^5.0.2"}, "dependencies": {"@adyen/adyen-web": "^5.71.2", "@adyen/api-library": "14.4.0", "@amille/simple-validators": "^1.1.1", "@ant-design/icons": "^5.6.1", "@ant-design/pro-layout": "^6.38.22", "@apollo/client": "3.7.15", "@aws-sdk/client-bedrock-agent-runtime": "^3.883.0", "@aws-sdk/client-rekognition": "^3.883.0", "@babel/runtime": "^7.28.4", "@bull-board/api": "^4.12.2", "@bull-board/express": "^4.12.2", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@faire/mjml-react": "^3.5.3", "@faker-js/faker": "^9.9.0", "@graphql-tools/schema": "^10.0.25", "@loadable/component": "^5.16.7", "@porsche-design-system/components-js": "^2.21.0", "@porsche-design-system/components-react": "3.23.0", "@promster/express": "^15.4.1", "@promster/server": "^15.4.1", "@sentry/node": "^9.46.0", "@sentry/profiling-node": "^9.46.0", "@sentry/react": "^9.46.0", "@types/ua-parser-js": "^0.7.39", "@uiw/react-json-view": "^2.0.0-alpha.37", "@uiw/react-md-editor": "^4.0.8", "@userlike/messenger": "^1.3.2", "accept-language-parser": "^1.5.0", "adm-zip": "^0.5.16", "antd": "5.x", "apollo-server": "^3.13.0", "apollo-server-core": "^3.13.0", "apollo-server-express": "^3.13.0", "apollo-upload-client": "^17.0.0", "archiver-zip-encrypted": "^2.0.0", "array-move": "^4.0.0", "bcryptjs": "^2.4.3", "bl": "^6.1.3", "broadcast-channel": "^7.1.0", "bson": "^6.10.4", "bull": "^4.16.5", "c8": "^10.1.3", "canvas": "^3.2.0", "chalk": "^4.1.2", "commander": "^14.0.0", "compression": "^1.8.1", "cookie": "^1.0.2", "cors": "^2.8.5", "countries-and-timezones": "^3.8.0", "currency-codes": "^2.2.0", "dataloader": "^2.2.3", "dayjs": "^1.11.18", "decimal.js": "^10.6.0", "docusign-esign": "^8.4.0", "echarts": "^5.6.0", "enquirer": "^2.4.1", "exceljs": "^4.4.0", "express": "^4.21.2", "extract-files": "^11.0.0", "fast-deep-equal": "^3.1.3", "fast-xml-parser": "^5.2.5", "fido2-lib": "^3.5.3", "file-saver": "^2.0.5", "form-data-encoder": "^1.9.0", "formdata-node": "^4.4.1", "formik": "^2.4.6", "graphql": "^16.11.0", "graphql-depth-limit": "^1.1.0", "graphql-redis-subscriptions": "^2.7.0", "graphql-subscriptions": "^2.0.0", "graphql-type-json": "^0.3.2", "graphql-upload": "^13.0.0", "graphql-ws": "^5.16.2", "html2canvas": "^1.4.1", "i18next": "^25.5.2", "i18next-fs-backend": "^2.6.0", "i18next-http-backend": "^3.0.2", "ics": "^3.8.1", "immutability-helper": "^3.1.1", "ioredis": "^5.7.0", "ipaddr.js": "^2.2.0", "jose": "^4.15.9", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.2", "libphonenumber-js": "^1.12.15", "lodash": "^4.17.21", "marked": "^14.1.4", "marked-gfm-heading-id": "^4.1.2", "marked-mangle": "^1.1.11", "minio": "^8.0.5", "mjml": "^4.15.3", "mjml-core": "^4.15.3", "mongodb": "^6.19.0", "mongodb-client-encryption": "^6.5.0", "morgan": "^1.10.1", "nanoid": "^3.3.11", "node-fetch": "^2.7.0", "nodemailer": "^6.10.1", "numeral": "^2.0.6", "nyc": "^17.1.0", "openapi-types": "^12.1.3", "openid-client": "^5.7.1", "openpgp": "^5.11.3", "otplib": "^12.0.1", "pdf-lib": "^1.17.1", "pdfjs-dist": "^3.11.174", "prom-client": "^15.1.3", "pubsub-js": "^1.9.5", "qrcode": "^1.5.4", "qs": "^6.14.0", "rate-limiter-flexible": "^7.3.0", "rc-cascader": "^3.34.0", "rc-picker": "^2.7.6", "rc-util": "^5.44.4", "react": "^18.3.1", "react-color": "^2.19.3", "react-country-flag": "^3.1.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-i18next": "^15.7.3", "react-idle-timer": "^5.7.2", "react-imask": "^7.6.1", "react-infinite-scroll-component": "^6.1.0", "react-is": "^18.3.1", "react-markdown": "^8.0.7", "react-pdf": "^8.0.2", "react-responsive-masonry": "2.7.1", "react-router": "^7.8.2", "react-sizeme": "^3.0.2", "react-spring-lightbox": "^1.8.0", "react-use": "^17.6.0", "sanitize-html": "^2.17.0", "sharp": "^0.34.3", "source-map-support": "^0.5.21", "ssh2-sftp-client": "^9.1.0", "styled-components": "^5.3.11", "twilio": "^4.23.0", "ua-parser-js": "^1.0.41", "url-join": "^4.0.1", "uuid": "^11.1.0", "ws": "^8.18.3", "xlsx-populate": "^1.21.0", "zod": "^4.1.5", "zxcvbn": "^4.4.2"}, "resolutions": {"@types/react": "^18.2.12", "@types/react-dom": "^18.2.5", "moment": "^2.29.4", "react": "^18.2.0", "react-dom": "^18.2.0", "ioredis": "^5.4.1"}}